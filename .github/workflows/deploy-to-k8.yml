name: Deploy Frontend to GKE
on:
  push:
    branches:
      - master
env:
  PROJECT_ID: keen-oasis-454518-n8
  REGION: us-east4
  SERVICE_NAME: tokencheck-frontend
  ENVIRONMENT: production
  DOMAIN: tokencheck.ai
  NEXT_PUBLIC_API_URL: https://api.tokencheck.ai/api
  NEXT_PUBLIC_SUBSCRIPTIONS_API_URL: https://subscriptions.tokencheck.ai/api
  NAMESPACE: develop
  REPOSITORY: tokencheck-repo
  LOCATION: us-east4
jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    environment: develop
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: Install gke-gcloud-auth-plugin
        run: |
          gcloud components install gke-gcloud-auth-plugin --quiet

      - name: Create Artifact Registry repository if not exists
        run: |
          # Check if repository exists
          if ! gcloud artifacts repositories describe ${{ env.REPOSITORY }} \
            --location=${{ env.LOCATION }} 2>/dev/null; then
            # Create repository if it doesn't exist
            gcloud artifacts repositories create ${{ env.REPOSITORY }} \
              --repository-format=docker \
              --location=${{ env.LOCATION }} \
              --description="Repository for Tokencheck container images"
          fi

      - name: Configure Docker for Artifact Registry
        run: |
          gcloud auth configure-docker ${{ env.LOCATION }}-docker.pkg.dev --quiet

      - name: Build and Push Docker Image
        run: |
          # Build with the Artifact Registry path
          docker build \
            --build-arg NEXT_PUBLIC_API_URL=${{ env.NEXT_PUBLIC_API_URL }} \
            --build-arg NEXT_PUBLIC_SUBSCRIPTIONS_API_URL=${{ env.NEXT_PUBLIC_SUBSCRIPTIONS_API_URL }} \
            -t ${{ env.LOCATION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE_NAME }}:latest .

          # Push to Artifact Registry
          docker push ${{ env.LOCATION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE_NAME }}:latest

      - name: Get GKE Credentials
        run: |
          gcloud container clusters get-credentials tokencheck-cluster \
            --region ${{ env.REGION }} \
            --project ${{ env.PROJECT_ID }}

      - name: Create Namespace if not exists
        run: |
          kubectl create namespace ${{ env.NAMESPACE }} --dry-run=client -o yaml | kubectl apply -f -

      - name: Create Artifact Registry Pull Secret
        run: |
          # Create service account key file
          echo '${{ secrets.GCR_PULL_SA_KEY }}' > gar-pull-sa-key.json

          # Create Kubernetes secret for Artifact Registry
          kubectl create secret docker-registry gar-pull-secret \
            --namespace ${{ env.NAMESPACE }} \
            --docker-server=${{ env.LOCATION }}-docker.pkg.dev \
            --docker-username=_json_key \
            --docker-password="$(cat gar-pull-sa-key.json)" \
            --dry-run=client -o yaml | kubectl apply -f -

          # Clean up key file
          rm gar-pull-sa-key.json

      - name: Update Image Reference in Deployment
        run: |
          # Update the image reference in the deployment YAML
          sed -i "s|gcr.io/${{ env.PROJECT_ID }}/${{ env.SERVICE_NAME }}:latest|${{ env.LOCATION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE_NAME }}:latest|g" k8s/tokencheck-frontend-deployment.yaml

          # Update the imagePullSecrets to use the new secret
          sed -i "s|gcr-pull-secret|gar-pull-secret|g" k8s/tokencheck-frontend-deployment.yaml

      - name: Deploy Frontend Service and Deployment
        run: |
          kubectl apply -f k8s/tokencheck-frontend-deployment.yaml
          kubectl apply -f k8s/tokencheck-frontend-service.yaml

      - name: Apply Kubernetes Resources
        run: |
          # Replace environment variables in Kubernetes manifests
          for file in k8s/frontend-certificate.yaml k8s/frontend-ingress.yaml k8s/frontend-config.yaml; do
            sed -i "s|\${DOMAIN}|${{ env.DOMAIN }}|g" $file
            kubectl apply -f $file
          done

      - name: Wait for Deployment
        run: |
          echo "Waiting for deployment to roll out..."
          kubectl rollout status deployment/tokencheck-frontend -n ${{ env.NAMESPACE }} --timeout=300s || true

      - name: Get External IP
        run: |
          echo "Waiting for Ingress to get an external IP (this may take a few minutes)..."

          # Try for up to 5 minutes to get the external IP
          for i in {1..30}; do
            EXTERNAL_IP=$(kubectl get ingress tokencheck-frontend-ingress -n ${{ env.NAMESPACE }} -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
            if [ -n "$EXTERNAL_IP" ]; then
              echo "Ingress external IP is: $EXTERNAL_IP"
              echo "Once DNS is configured, your frontend will be available at: https://${{ env.DOMAIN }}"
              break
            fi
            echo "Waiting for external IP... (attempt $i/30)"
            sleep 10
          done

          if [ -z "$EXTERNAL_IP" ]; then
            echo "Could not get external IP within the timeout period. Check the ingress status manually."
          fi

      - name: Verify Deployment
        run: |
          echo "Checking frontend deployment status..."
          kubectl get deployment tokencheck-frontend -n ${{ env.NAMESPACE }}

          echo "Checking pods..."
          kubectl get pods -n ${{ env.NAMESPACE }} -l app=tokencheck-frontend

          echo "Checking service..."
          kubectl get service tokencheck-frontend -n ${{ env.NAMESPACE }}

          echo "Checking ingress..."
          kubectl get ingress tokencheck-frontend-ingress -n ${{ env.NAMESPACE }}
