apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: tokencheck-frontend-ingress
  namespace: develop
  annotations:
    kubernetes.io/ingress.class: "gce"
    networking.gke.io/managed-certificates: "tokencheck-frontend-cert"
    networking.gke.io/v1beta1.FrontendConfig: "tokencheck-frontend-config"
spec:
  rules:
  - host: ${DOMAIN}
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: tokencheck-frontend
            port:
              number: 3000