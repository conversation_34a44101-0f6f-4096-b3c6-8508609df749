import * as React from "react";
import type { FormData } from "./form";

interface EmailTemplateProps {
  content: FormData;
}

export function EmailTemplate({ content }: EmailTemplateProps) {
  return (
    <div className="bg-gray-50 text-gray-800 font-sans p-6">
      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
        {/* Header */}
        <div className="bg-blue-600 text-white px-6 py-4">
          <h2 className="text-lg font-semibold">
            🚨 New Audit Request Received
          </h2>
        </div>

        {/* Body */}
        <div className="p-6 space-y-6">
          {/* Contact Information */}
          <div>
            <h3 className="text-md font-bold mb-2">🔗 Contact Information</h3>
            <p><span className="font-medium">Name:</span> {content.name}</p>
            <p><span className="font-medium">Email:</span> {content.email}</p>
            {content.company && (
              <p><span className="font-medium">Company:</span> {content.company}</p>
            )}
          </div>

          {/* Audit Objectives */}
          <div>
            <h3 className="text-md font-bold mb-2">🎯 Audit Objectives</h3>
            <ul className="list-disc list-inside space-y-1">
              {content.primaryGoals.map((goal, index) => (
                <li key={index}>{goal}</li>
              ))}
            </ul>
          </div>

          {/* Build Configuration */}
          <div>
            <h3 className="text-md font-bold mb-2">🛠 Build Configuration</h3>
            <p><span className="font-medium">Build Succeeds:</span> {content.buildSucceeds}</p>
            <p><span className="font-medium">Framework:</span> {content.framework}</p>
            {content.frameworkVersion && (
              <p><span className="font-medium">Framework Version:</span> {content.frameworkVersion}</p>
            )}
          </div>

          {/* Documentation & Code Quality */}
          <div>
            <h3 className="text-md font-bold mb-2">📄 Documentation & Code Quality</h3>
            <p><span className="font-medium">README:</span> {content.hasReadme}</p>
            <p><span className="font-medium">Natspec:</span> {content.hasNatspec}</p>
            {/* <p><span className="font-medium">Sequence Diagrams:</span> {content.hasSequenceDiagrams}</p> */}
            {/* <p><span className="font-medium">Commits Pinned:</span> {content.commitsPinned}</p> */}
            {/* <div className="mt-2"> */}
            {/*   <p className="font-medium">Imported Libraries:</p> */}
            {/*   <pre className="bg-gray-100 text-gray-700 p-3 rounded text-sm overflow-x-auto"> */}
            {/*     {content.importedLibraries} */}
            {/*   </pre> */}
            {/* </div> */}
          </div>

          {/* Footer */}
          <p className="text-xs text-gray-500 pt-4 border-t">
            📧 This request was automatically generated by your audit form.
          </p>
        </div>
      </div>
    </div>
  );
}
