/**
 * Token Analysis Page
 *
 * This page displays detailed security analysis for a specific token address.
 * It uses dynamic routing to capture the token address from the URL.
 *
 * It uses the TokenAnalysisClient component which handles data fetching with SWR
 * for caching, automatic revalidation, and better error handling.
 */

import TokenAnalysisClient from './token-analysis-client';

/**
 * Generate static parameters for common token addresses
 * This improves performance by pre-rendering pages for popular tokens
 *
 * @returns Array of token address parameters for static generation
 */
export function generateStaticParams() {
  return [
    { tokenAddress: '******************************************' }, // WBTC
    { tokenAddress: '******************************************' }, // WETH
    { tokenAddress: '******************************************' }, // USDC
    { tokenAddress: '******************************************' }, // DAI
    { tokenAddress: '******************************************' }, // Example token
  ];
}

/**
 * Token page component that displays analysis for a specific token
 *
 * @param params - Object containing the tokenAddress from the URL
 * @returns TokenAnalysisClient component with the token address
 */
export default function TokenPage({ params }: { params: { tokenAddress: string } }) {
  return <TokenAnalysisClient tokenAddress={params.tokenAddress} />;
}